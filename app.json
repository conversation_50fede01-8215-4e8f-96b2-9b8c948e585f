{"expo": {"name": "AlemdarControler", "slug": "AlemdarControler", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/splash-icon.png", "scheme": "com.alemdarteknik", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.alemdarteknik.app", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/splash-icon.png", "backgroundColor": "#000000"}, "package": "com.alemdarteknik.app", "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/splash-icon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#000000"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "aac50e16-0846-408b-9e1f-da5d6c423508"}}}}